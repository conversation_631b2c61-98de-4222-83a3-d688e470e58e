# OCCI Library Makefile
# Compatible with orapp interface

# Compiler settings
CXX = g++
CXXFLAGS = -Wall -O2 -fPIC -D_REENTRANT=1

# Oracle 23ai settings
ORACLE_HOME ?= /opt/oracle/product/23ai/dbhomeFree
ORACLE_INCLUDE ?= /opt/oracle/product/23ai/dbhomeFree/rdbms/public

# Include directories
INCLUDES = -I. -I$(ORACLE_INCLUDE)

# Library directories and libraries
LIBDIRS = -L$(ORACLE_HOME)/lib
LIBS = -locci -lclntsh

# Source files
SOURCES = constants.cpp log.cpp error.cpp field.cpp row.cpp query.cpp conn.cpp
OBJECTS = $(SOURCES:.cpp=.o)

# Target library
TARGET = lib/libocciwrapper.a

# Default target
all: $(TARGET)

# Create lib directory
lib:
	mkdir -p lib

# Build library
$(TARGET): lib $(OBJECTS)
	ar rcs $@ $(OBJECTS)
	@echo "Library $(TARGET) created successfully"

# Compile source files
%.o: %.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Clean build files
clean:
	rm -f $(OBJECTS) $(TARGET) test_occi lib/libocci.a
	@echo "Clean completed"

# Clean all including lib directory
distclean: clean
	rm -rf lib
	@echo "Distribution clean completed"

# Install (copy to system location if needed)
install: $(TARGET)
	@echo "Install target not implemented"

# Test build
test: $(TARGET) test_occi
	@echo "Test program built successfully"

# Build test program
test_occi: test_occi.cpp $(TARGET)
	$(CXX) $(CXXFLAGS) $(INCLUDES) test_occi.cpp -o test_occi -L./lib -locciwrapper $(LIBDIRS) -locci -lclntsh -Wl,-rpath,$(ORACLE_HOME)/lib

# Show configuration
config:
	@echo "Configuration:"
	@echo "  CXX: $(CXX)"
	@echo "  CXXFLAGS: $(CXXFLAGS)"
	@echo "  ORACLE_HOME: $(ORACLE_HOME)"
	@echo "  ORACLE_INCLUDE: $(ORACLE_INCLUDE)"
	@echo "  INCLUDES: $(INCLUDES)"
	@echo "  LIBDIRS: $(LIBDIRS)"
	@echo "  LIBS: $(LIBS)"
	@echo "  TARGET: $(TARGET)"

# Dependencies
constants.o: constants.cpp constants.hh
log.o: log.cpp log.hh
error.o: error.cpp error.hh log.hh
field.o: field.cpp field.hh error.hh constants.hh
row.o: row.cpp row.hh field.hh error.hh constants.hh
query.o: query.cpp query.hh row.hh field.hh error.hh log.hh constants.hh
conn.o: conn.cpp conn.hh query.hh error.hh log.hh constants.hh

.PHONY: all clean distclean install test config lib
