
/* Result Sets Interface */
#ifndef SQL_CRSR
#  define SQL_CRSR
  struct sql_cursor
  {
    unsigned int curocn;
    void *ptr1;
    void *ptr2;
    unsigned int magic;
  };
  typedef struct sql_cursor sql_cursor;
  typedef struct sql_cursor SQL_CURSOR;
#endif /* SQL_CRSR */

/* Thread Safety */
typedef void * sql_context;
typedef void * SQL_CONTEXT;

/* Object support */
struct sqltvn
{
  unsigned char *tvnvsn; 
  unsigned short tvnvsnl; 
  unsigned char *tvnnm;
  unsigned short tvnnml; 
  unsigned char *tvnsnm;
  unsigned short tvnsnml;
};
typedef struct sqltvn sqltvn;

struct sqladts
{
  unsigned int adtvsn; 
  unsigned short adtmode; 
  unsigned short adtnum;  
  sqltvn adttvn[1];       
};
typedef struct sqladts sqladts;

static struct sqladts sqladt = {
  1,0,0,
};

/* Binding to PL/SQL Records */
struct sqltdss
{
  unsigned int tdsvsn; 
  unsigned short tdsnum; 
  unsigned char *tdsval[1]; 
};
typedef struct sqltdss sqltdss;
static struct sqltdss sqltds =
{
  1,
  0,
};

/* File name & Package Name */
struct sqlcxp
{
  unsigned short fillen;
           char  filnam[18];
};
static const struct sqlcxp sqlfpn =
{
    17,
    "logonDB_simple.pc"
};


static unsigned int sqlctx = 9995075;


static struct sqlexd {
   unsigned long  sqlvsn;
   unsigned int   arrsiz;
   unsigned int   iters;
   unsigned int   offset;
   unsigned short selerr;
   unsigned short sqlety;
   unsigned int   occurs;
      const short *cud;
   unsigned char  *sqlest;
      const char  *stmt;
   sqladts *sqladtp;
   sqltdss *sqltdsp;
   unsigned char  **sqphsv;
   unsigned long  *sqphsl;
            int   *sqphss;
            short **sqpind;
            int   *sqpins;
   unsigned long  *sqparm;
   unsigned long  **sqparc;
   unsigned short  *sqpadto;
   unsigned short  *sqptdso;
   unsigned int   sqlcmax;
   unsigned int   sqlcmin;
   unsigned int   sqlcincr;
   unsigned int   sqlctimeout;
   unsigned int   sqlcnowait;
            int   sqfoff;
   unsigned int   sqcmod;
   unsigned int   sqfmod;
   unsigned int   sqlpfmem;
   unsigned char  *sqhstv[20];
   unsigned long  sqhstl[20];
            int   sqhsts[20];
            short *sqindv[20];
            int   sqinds[20];
   unsigned long  sqharm[20];
   unsigned long  *sqharc[20];
   unsigned short  sqadto[20];
   unsigned short  sqtdso[20];
} sqlstm = {13,20};

// Prototypes
extern "C" {
  void sqlcxt (void **, unsigned int *,
               struct sqlexd *, const struct sqlcxp *);
  void sqlcx2t(void **, unsigned int *,
               struct sqlexd *, const struct sqlcxp *);
  void sqlbuft(void **, char *);
  void sqlgs2t(void **, char *);
  void sqlorat(void **, unsigned int *, void *);
}

// Forms Interface
static const int IAPSUCC = 0;
static const int IAPFAIL = 1403;
static const int IAPFTL  = 535;
extern "C" { void sqliem(unsigned char *, signed int *); }

 static const char *sq0005 = 
"select PTN_ID ,CALLBACK  from TBL_CALLBACK where PTN_ID=:b0           ";

 static const char *sq0006 = 
"select DIAL_CODE_TYPE ,DIAL_CODE  from TBL_ALLOW_DIAL_CODE where DIAL_CODE_T\
YPE=:b0           ";

typedef struct { unsigned short len; unsigned char arr[1]; } VARCHAR;
typedef struct { unsigned short len; unsigned char arr[1]; } varchar;

/* cud (compilation unit data) array */
static const short sqlcud0[] =
{13,4114,1,0,0,
5,0,0,0,0,0,27,99,0,0,4,4,0,1,0,1,97,0,0,1,97,0,0,1,97,0,0,1,10,0,0,
36,0,0,2,0,0,30,113,0,0,0,0,0,1,0,
51,0,0,3,121,0,122,159,0,0,20,20,0,1,0,1,97,0,0,1,97,0,0,1,97,0,0,1,97,0,0,1,3,
0,0,1,3,0,0,1,3,0,0,1,3,0,0,1,3,0,0,1,3,0,0,1,97,0,0,1,3,0,0,1,97,0,0,1,97,0,0,
1,97,0,0,1,97,0,0,1,97,0,0,1,97,0,0,1,3,0,0,1,97,0,0,
146,0,0,4,69,0,122,236,0,0,11,11,0,1,0,1,3,0,0,1,97,0,0,1,3,0,0,1,3,0,0,1,3,0,
0,1,3,0,0,1,97,0,0,1,3,0,0,1,3,0,0,1,3,0,0,1,97,0,0,
205,0,0,5,70,0,9,285,0,0,1,1,0,1,0,1,3,0,0,
224,0,0,5,0,0,13,293,0,0,2,0,0,1,0,2,3,0,0,2,97,0,0,
247,0,0,5,0,0,15,301,0,0,0,0,0,1,0,
262,0,0,5,0,0,15,317,0,0,0,0,0,1,0,
277,0,0,6,94,0,9,334,0,0,1,1,0,1,0,1,97,0,0,
296,0,0,6,0,0,13,342,0,0,2,0,0,1,0,2,97,0,0,2,97,0,0,
319,0,0,6,0,0,15,350,0,0,0,0,0,1,0,
334,0,0,6,0,0,15,366,0,0,0,0,0,1,0,
};


/*
 * logonDB_simple.pc - Pro*C version of logonDB (C style)
 * Converted from ODPI-C to Pro*C for better Oracle compatibility
 */

#include "logonDB_proc_c.h"
#include <sqlca.h>

// Pro*C SQL Communication Area - using direct include instead of EXEC SQL INCLUDE

// Pro*C 전역 변수들
/* EXEC SQL BEGIN DECLARE SECTION; */ 

    // 연결 정보
    char db_userid[64];
    char db_passwd[64]; 
    char db_database[64];
    
    // proc_check_mms_login_ext2 변수들
    char proc1_in_cid[64];
    char proc1_in_pwd[64];
    char proc1_ot_appname[256];
    char proc1_ot_sip[64];
    int proc1_ot_pid;
    int proc1_ot_job;
    int proc1_ot_c_job;
    int proc1_ot_prt;
    int proc1_ot_cnt;
    int proc1_ot_rpt_cnt;
    char proc1_ot_server_info[512];
    int proc1_ot_rpt_sleep_cnt;
    char proc1_ot_sender_proc[256];
    char proc1_ot_report_proc[256];
    char proc1_ot_senderdb_info[512];
    char proc1_ot_reportdb_info[512];
    char proc1_ot_logfile_info[512];
    char proc1_ot_etc[512];
    int proc1_ot_rst;
    char proc1_ot_rstmsg[512];
    
    // proc_get_limit_def 변수들
    int proc2_in_pid;
    char proc2_ot_limit_type[256];
    int proc2_ot_day_warn_cnt;
    int proc2_ot_day_limit_cnt;
    int proc2_ot_mon_warn_cnt;
    int proc2_ot_mon_limit_cnt;
    char proc2_ot_limit_flag[256];
    int proc2_ot_day_acc_cnt;
    int proc2_ot_mon_acc_cnt;
    int proc2_ot_rst;
    char proc2_ot_rstmsg[512];
    
    // SELECT 쿼리 변수들
    int select_ptn_id;
    char select_callback[512];
    char select_dial_code_type[64];
    char select_dial_code[64];
/* EXEC SQL END DECLARE SECTION; */ 


// 기존 전역 변수들
extern int activeProcess;
extern struct _message_info message_info;
extern struct _shm_info *shm_info;
extern char PROCESS_NO[7], PROCESS_NAME[36];

int hNewSocket = -1;
int ret = 0;

// 설정 정보
CConfigLogonDB gConf;

// 시그널 핸들러들
void alarm_handler(int sig) {
    log_history(0,0,"[WARN] Alarm signal received (signal %d)", sig);
}

void segv_handler(int sig) {
    log_history(0,0,"[FATAL] Segmentation fault detected (signal %d)", sig);
    log_history(0,0,"[FATAL] Process will terminate");
    activeProcess = 0;
    exit(1);
}

void term_handler(int sig) {
    log_history(0,0,"[INFO] Termination signal received (signal %d)", sig);
    log_history(0,0,"[INFO] Initiating graceful shutdown...");
    activeProcess = 0;
}

// Pro*C 데이터베이스 연결 함수
int proc_connect() {
    // 연결 정보 설정
    strcpy(db_userid, gConf.dbID);
    strcpy(db_passwd, gConf.dbPASS);
    strcpy(db_database, gConf.dbSID);
    
    log_history(0,0,"[INF] Attempting Pro*C connection to %s", db_database);
    
    /* EXEC SQL CONNECT :db_userid IDENTIFIED BY :db_passwd USING :db_database; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 4;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )10;
    sqlstm.offset = (unsigned int  )5;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)db_userid;
    sqlstm.sqhstl[0] = (unsigned long )64;
    sqlstm.sqhsts[0] = (         int  )64;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)db_passwd;
    sqlstm.sqhstl[1] = (unsigned long )64;
    sqlstm.sqhsts[1] = (         int  )64;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)db_database;
    sqlstm.sqhstl[2] = (unsigned long )64;
    sqlstm.sqhsts[2] = (         int  )64;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlstm.sqlcmax = (unsigned int )100;
    sqlstm.sqlcmin = (unsigned int )2;
    sqlstm.sqlcincr = (unsigned int )1;
    sqlstm.sqlctimeout = (unsigned int )0;
    sqlstm.sqlcnowait = (unsigned int )0;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}


    
    if (sqlca.sqlcode != 0) {
        log_history(0,0,"[ERR] Pro*C connect failed - SQLCODE: %d", sqlca.sqlcode);
        log_history(0,0,"[ERR] SQLERRM: %.70s", sqlca.sqlerrm.sqlerrmc);
        return FALSE;
    }
    
    log_history(0,0,"[INF] Pro*C db connect success");
    return TRUE;
}

// Pro*C 데이터베이스 연결 해제 함수
void proc_disconnect() {
    /* EXEC SQL COMMIT WORK RELEASE; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 4;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )36;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}


    
    if (sqlca.sqlcode != 0) {
        log_history(0,0,"[WARN] Pro*C disconnect warning - SQLCODE: %d", sqlca.sqlcode);
    } else {
        log_history(0,0,"[INF] Pro*C db disconnect success");
    }
}

// proc_check_mms_login_ext2 호출 함수
int call_proc_check_mms_login_ext2(const char* cid, const char* pwd, 
                                   char* szAPPName, char* szSIP, int* nmPID, int* nmJOB,
                                   int* nUrlJob, int* nmPRT, int* nmCNT, int* nRptWait,
                                   char* szServerInfo, int* nRptNoDataSleep,
                                   char* szSenderName, char* szReportName,
                                   char* szSenderDBName, char* szReportDBName,
                                   char* szLogFilePath, char* szReserve,
                                   int* nmRST, char* szErrMsg) {
    
    // 입력 파라미터 설정
    strcpy(proc1_in_cid, cid);
    strcpy(proc1_in_pwd, pwd);
    
    // 출력 파라미터 초기화
    memset(proc1_ot_appname, 0, sizeof(proc1_ot_appname));
    memset(proc1_ot_sip, 0, sizeof(proc1_ot_sip));
    proc1_ot_pid = 0;
    proc1_ot_job = 0;
    proc1_ot_c_job = 0;
    proc1_ot_prt = 0;
    proc1_ot_cnt = 0;
    proc1_ot_rpt_cnt = 0;
    memset(proc1_ot_server_info, 0, sizeof(proc1_ot_server_info));
    proc1_ot_rpt_sleep_cnt = *nRptNoDataSleep;
    memset(proc1_ot_sender_proc, 0, sizeof(proc1_ot_sender_proc));
    memset(proc1_ot_report_proc, 0, sizeof(proc1_ot_report_proc));
    memset(proc1_ot_senderdb_info, 0, sizeof(proc1_ot_senderdb_info));
    memset(proc1_ot_reportdb_info, 0, sizeof(proc1_ot_reportdb_info));
    memset(proc1_ot_logfile_info, 0, sizeof(proc1_ot_logfile_info));
    memset(proc1_ot_etc, 0, sizeof(proc1_ot_etc));
    proc1_ot_rst = -999;
    memset(proc1_ot_rstmsg, 0, sizeof(proc1_ot_rstmsg));
    
    log_history(0,0,"[INF] Calling proc_check_mms_login_ext2 for CID[%s] PWD[%s]", cid, pwd);
    
    // 프로시저 호출
    /* EXEC SQL CALL proc_check_mms_login_ext2(
        :proc1_in_cid,
        :proc1_in_pwd,
        :proc1_ot_appname,
        :proc1_ot_sip,
        :proc1_ot_pid,
        :proc1_ot_job,
        :proc1_ot_c_job,
        :proc1_ot_prt,
        :proc1_ot_cnt,
        :proc1_ot_rpt_cnt,
        :proc1_ot_server_info,
        :proc1_ot_rpt_sleep_cnt,
        :proc1_ot_sender_proc,
        :proc1_ot_report_proc,
        :proc1_ot_senderdb_info,
        :proc1_ot_reportdb_info,
        :proc1_ot_logfile_info,
        :proc1_ot_etc,
        :proc1_ot_rst,
        :proc1_ot_rstmsg
    ); */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 20;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "call proc_check_mms_login_ext2(:b0,:b1,:b2,:b3,:b4,:b5,:b\
6,:b7,:b8,:b9,:b10,:b11,:b12,:b13,:b14,:b15,:b16,:b17,:b18,:b19)";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )51;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)proc1_in_cid;
    sqlstm.sqhstl[0] = (unsigned long )64;
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)proc1_in_pwd;
    sqlstm.sqhstl[1] = (unsigned long )64;
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)proc1_ot_appname;
    sqlstm.sqhstl[2] = (unsigned long )256;
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqhstv[3] = (unsigned char  *)proc1_ot_sip;
    sqlstm.sqhstl[3] = (unsigned long )64;
    sqlstm.sqhsts[3] = (         int  )0;
    sqlstm.sqindv[3] = (         short *)0;
    sqlstm.sqinds[3] = (         int  )0;
    sqlstm.sqharm[3] = (unsigned long )0;
    sqlstm.sqadto[3] = (unsigned short )0;
    sqlstm.sqtdso[3] = (unsigned short )0;
    sqlstm.sqhstv[4] = (unsigned char  *)&proc1_ot_pid;
    sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[4] = (         int  )0;
    sqlstm.sqindv[4] = (         short *)0;
    sqlstm.sqinds[4] = (         int  )0;
    sqlstm.sqharm[4] = (unsigned long )0;
    sqlstm.sqadto[4] = (unsigned short )0;
    sqlstm.sqtdso[4] = (unsigned short )0;
    sqlstm.sqhstv[5] = (unsigned char  *)&proc1_ot_job;
    sqlstm.sqhstl[5] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[5] = (         int  )0;
    sqlstm.sqindv[5] = (         short *)0;
    sqlstm.sqinds[5] = (         int  )0;
    sqlstm.sqharm[5] = (unsigned long )0;
    sqlstm.sqadto[5] = (unsigned short )0;
    sqlstm.sqtdso[5] = (unsigned short )0;
    sqlstm.sqhstv[6] = (unsigned char  *)&proc1_ot_c_job;
    sqlstm.sqhstl[6] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[6] = (         int  )0;
    sqlstm.sqindv[6] = (         short *)0;
    sqlstm.sqinds[6] = (         int  )0;
    sqlstm.sqharm[6] = (unsigned long )0;
    sqlstm.sqadto[6] = (unsigned short )0;
    sqlstm.sqtdso[6] = (unsigned short )0;
    sqlstm.sqhstv[7] = (unsigned char  *)&proc1_ot_prt;
    sqlstm.sqhstl[7] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[7] = (         int  )0;
    sqlstm.sqindv[7] = (         short *)0;
    sqlstm.sqinds[7] = (         int  )0;
    sqlstm.sqharm[7] = (unsigned long )0;
    sqlstm.sqadto[7] = (unsigned short )0;
    sqlstm.sqtdso[7] = (unsigned short )0;
    sqlstm.sqhstv[8] = (unsigned char  *)&proc1_ot_cnt;
    sqlstm.sqhstl[8] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[8] = (         int  )0;
    sqlstm.sqindv[8] = (         short *)0;
    sqlstm.sqinds[8] = (         int  )0;
    sqlstm.sqharm[8] = (unsigned long )0;
    sqlstm.sqadto[8] = (unsigned short )0;
    sqlstm.sqtdso[8] = (unsigned short )0;
    sqlstm.sqhstv[9] = (unsigned char  *)&proc1_ot_rpt_cnt;
    sqlstm.sqhstl[9] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[9] = (         int  )0;
    sqlstm.sqindv[9] = (         short *)0;
    sqlstm.sqinds[9] = (         int  )0;
    sqlstm.sqharm[9] = (unsigned long )0;
    sqlstm.sqadto[9] = (unsigned short )0;
    sqlstm.sqtdso[9] = (unsigned short )0;
    sqlstm.sqhstv[10] = (unsigned char  *)proc1_ot_server_info;
    sqlstm.sqhstl[10] = (unsigned long )512;
    sqlstm.sqhsts[10] = (         int  )0;
    sqlstm.sqindv[10] = (         short *)0;
    sqlstm.sqinds[10] = (         int  )0;
    sqlstm.sqharm[10] = (unsigned long )0;
    sqlstm.sqadto[10] = (unsigned short )0;
    sqlstm.sqtdso[10] = (unsigned short )0;
    sqlstm.sqhstv[11] = (unsigned char  *)&proc1_ot_rpt_sleep_cnt;
    sqlstm.sqhstl[11] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[11] = (         int  )0;
    sqlstm.sqindv[11] = (         short *)0;
    sqlstm.sqinds[11] = (         int  )0;
    sqlstm.sqharm[11] = (unsigned long )0;
    sqlstm.sqadto[11] = (unsigned short )0;
    sqlstm.sqtdso[11] = (unsigned short )0;
    sqlstm.sqhstv[12] = (unsigned char  *)proc1_ot_sender_proc;
    sqlstm.sqhstl[12] = (unsigned long )256;
    sqlstm.sqhsts[12] = (         int  )0;
    sqlstm.sqindv[12] = (         short *)0;
    sqlstm.sqinds[12] = (         int  )0;
    sqlstm.sqharm[12] = (unsigned long )0;
    sqlstm.sqadto[12] = (unsigned short )0;
    sqlstm.sqtdso[12] = (unsigned short )0;
    sqlstm.sqhstv[13] = (unsigned char  *)proc1_ot_report_proc;
    sqlstm.sqhstl[13] = (unsigned long )256;
    sqlstm.sqhsts[13] = (         int  )0;
    sqlstm.sqindv[13] = (         short *)0;
    sqlstm.sqinds[13] = (         int  )0;
    sqlstm.sqharm[13] = (unsigned long )0;
    sqlstm.sqadto[13] = (unsigned short )0;
    sqlstm.sqtdso[13] = (unsigned short )0;
    sqlstm.sqhstv[14] = (unsigned char  *)proc1_ot_senderdb_info;
    sqlstm.sqhstl[14] = (unsigned long )512;
    sqlstm.sqhsts[14] = (         int  )0;
    sqlstm.sqindv[14] = (         short *)0;
    sqlstm.sqinds[14] = (         int  )0;
    sqlstm.sqharm[14] = (unsigned long )0;
    sqlstm.sqadto[14] = (unsigned short )0;
    sqlstm.sqtdso[14] = (unsigned short )0;
    sqlstm.sqhstv[15] = (unsigned char  *)proc1_ot_reportdb_info;
    sqlstm.sqhstl[15] = (unsigned long )512;
    sqlstm.sqhsts[15] = (         int  )0;
    sqlstm.sqindv[15] = (         short *)0;
    sqlstm.sqinds[15] = (         int  )0;
    sqlstm.sqharm[15] = (unsigned long )0;
    sqlstm.sqadto[15] = (unsigned short )0;
    sqlstm.sqtdso[15] = (unsigned short )0;
    sqlstm.sqhstv[16] = (unsigned char  *)proc1_ot_logfile_info;
    sqlstm.sqhstl[16] = (unsigned long )512;
    sqlstm.sqhsts[16] = (         int  )0;
    sqlstm.sqindv[16] = (         short *)0;
    sqlstm.sqinds[16] = (         int  )0;
    sqlstm.sqharm[16] = (unsigned long )0;
    sqlstm.sqadto[16] = (unsigned short )0;
    sqlstm.sqtdso[16] = (unsigned short )0;
    sqlstm.sqhstv[17] = (unsigned char  *)proc1_ot_etc;
    sqlstm.sqhstl[17] = (unsigned long )512;
    sqlstm.sqhsts[17] = (         int  )0;
    sqlstm.sqindv[17] = (         short *)0;
    sqlstm.sqinds[17] = (         int  )0;
    sqlstm.sqharm[17] = (unsigned long )0;
    sqlstm.sqadto[17] = (unsigned short )0;
    sqlstm.sqtdso[17] = (unsigned short )0;
    sqlstm.sqhstv[18] = (unsigned char  *)&proc1_ot_rst;
    sqlstm.sqhstl[18] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[18] = (         int  )0;
    sqlstm.sqindv[18] = (         short *)0;
    sqlstm.sqinds[18] = (         int  )0;
    sqlstm.sqharm[18] = (unsigned long )0;
    sqlstm.sqadto[18] = (unsigned short )0;
    sqlstm.sqtdso[18] = (unsigned short )0;
    sqlstm.sqhstv[19] = (unsigned char  *)proc1_ot_rstmsg;
    sqlstm.sqhstl[19] = (unsigned long )512;
    sqlstm.sqhsts[19] = (         int  )0;
    sqlstm.sqindv[19] = (         short *)0;
    sqlstm.sqinds[19] = (         int  )0;
    sqlstm.sqharm[19] = (unsigned long )0;
    sqlstm.sqadto[19] = (unsigned short )0;
    sqlstm.sqtdso[19] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}


    
    if (sqlca.sqlcode != 0) {
        log_history(0,0,"[ERR] proc_check_mms_login_ext2 failed - SQLCODE: %d", sqlca.sqlcode);
        log_history(0,0,"[ERR] SQLERRM: %.70s", sqlca.sqlerrm.sqlerrmc);
        return FALSE;
    }
    
    // 결과 복사
    strcpy(szAPPName, proc1_ot_appname);
    strcpy(szSIP, proc1_ot_sip);
    *nmPID = proc1_ot_pid;
    *nmJOB = proc1_ot_job;
    *nUrlJob = proc1_ot_c_job;
    *nmPRT = proc1_ot_prt;
    *nmCNT = proc1_ot_cnt;
    *nRptWait = proc1_ot_rpt_cnt;
    strcpy(szServerInfo, proc1_ot_server_info);
    *nRptNoDataSleep = proc1_ot_rpt_sleep_cnt;
    strcpy(szSenderName, proc1_ot_sender_proc);
    strcpy(szReportName, proc1_ot_report_proc);
    strcpy(szSenderDBName, proc1_ot_senderdb_info);
    strcpy(szReportDBName, proc1_ot_reportdb_info);
    strcpy(szLogFilePath, proc1_ot_logfile_info);
    strcpy(szReserve, proc1_ot_etc);
    *nmRST = proc1_ot_rst;
    strcpy(szErrMsg, proc1_ot_rstmsg);
    
    log_history(0,0,"[INF] proc_check_mms_login_ext2 executed successfully, nmRST[%d] ErrMsg[%s]", *nmRST, szErrMsg);
    
    return TRUE;
}

// proc_get_limit_def 호출 함수  
int call_proc_get_limit_def(int pid, char* szLimitType, int* nDayWarnCnt, int* nDayLimitCnt,
                            int* nMonWarnCnt, int* nMonLimitCnt, char* szLimitFlag,
                            int* nDayAccCnt, int* nMonAccCnt, int* nmRST, char* szErrMsg) {
    
    // 입력 파라미터 설정
    proc2_in_pid = pid;
    
    // 출력 파라미터 초기화
    memset(proc2_ot_limit_type, 0, sizeof(proc2_ot_limit_type));
    proc2_ot_day_warn_cnt = 0;
    proc2_ot_day_limit_cnt = 0;
    proc2_ot_mon_warn_cnt = 0;
    proc2_ot_mon_limit_cnt = 0;
    memset(proc2_ot_limit_flag, 0, sizeof(proc2_ot_limit_flag));
    proc2_ot_day_acc_cnt = 0;
    proc2_ot_mon_acc_cnt = 0;
    proc2_ot_rst = -999;
    memset(proc2_ot_rstmsg, 0, sizeof(proc2_ot_rstmsg));
    
    log_history(0,0,"[INF] Calling proc_get_limit_def for PID[%d]", pid);
    
    // 프로시저 호출
    /* EXEC SQL CALL proc_get_limit_def(
        :proc2_in_pid,
        :proc2_ot_limit_type,
        :proc2_ot_day_warn_cnt,
        :proc2_ot_day_limit_cnt,
        :proc2_ot_mon_warn_cnt,
        :proc2_ot_mon_limit_cnt,
        :proc2_ot_limit_flag,
        :proc2_ot_day_acc_cnt,
        :proc2_ot_mon_acc_cnt,
        :proc2_ot_rst,
        :proc2_ot_rstmsg
    ); */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 20;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "call proc_get_limit_def(:b0,:b1,:b2,:b3,:b4,:b5,:b6,:b7,:\
b8,:b9,:b10)";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )146;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&proc2_in_pid;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)proc2_ot_limit_type;
    sqlstm.sqhstl[1] = (unsigned long )256;
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)&proc2_ot_day_warn_cnt;
    sqlstm.sqhstl[2] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqhstv[3] = (unsigned char  *)&proc2_ot_day_limit_cnt;
    sqlstm.sqhstl[3] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[3] = (         int  )0;
    sqlstm.sqindv[3] = (         short *)0;
    sqlstm.sqinds[3] = (         int  )0;
    sqlstm.sqharm[3] = (unsigned long )0;
    sqlstm.sqadto[3] = (unsigned short )0;
    sqlstm.sqtdso[3] = (unsigned short )0;
    sqlstm.sqhstv[4] = (unsigned char  *)&proc2_ot_mon_warn_cnt;
    sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[4] = (         int  )0;
    sqlstm.sqindv[4] = (         short *)0;
    sqlstm.sqinds[4] = (         int  )0;
    sqlstm.sqharm[4] = (unsigned long )0;
    sqlstm.sqadto[4] = (unsigned short )0;
    sqlstm.sqtdso[4] = (unsigned short )0;
    sqlstm.sqhstv[5] = (unsigned char  *)&proc2_ot_mon_limit_cnt;
    sqlstm.sqhstl[5] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[5] = (         int  )0;
    sqlstm.sqindv[5] = (         short *)0;
    sqlstm.sqinds[5] = (         int  )0;
    sqlstm.sqharm[5] = (unsigned long )0;
    sqlstm.sqadto[5] = (unsigned short )0;
    sqlstm.sqtdso[5] = (unsigned short )0;
    sqlstm.sqhstv[6] = (unsigned char  *)proc2_ot_limit_flag;
    sqlstm.sqhstl[6] = (unsigned long )256;
    sqlstm.sqhsts[6] = (         int  )0;
    sqlstm.sqindv[6] = (         short *)0;
    sqlstm.sqinds[6] = (         int  )0;
    sqlstm.sqharm[6] = (unsigned long )0;
    sqlstm.sqadto[6] = (unsigned short )0;
    sqlstm.sqtdso[6] = (unsigned short )0;
    sqlstm.sqhstv[7] = (unsigned char  *)&proc2_ot_day_acc_cnt;
    sqlstm.sqhstl[7] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[7] = (         int  )0;
    sqlstm.sqindv[7] = (         short *)0;
    sqlstm.sqinds[7] = (         int  )0;
    sqlstm.sqharm[7] = (unsigned long )0;
    sqlstm.sqadto[7] = (unsigned short )0;
    sqlstm.sqtdso[7] = (unsigned short )0;
    sqlstm.sqhstv[8] = (unsigned char  *)&proc2_ot_mon_acc_cnt;
    sqlstm.sqhstl[8] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[8] = (         int  )0;
    sqlstm.sqindv[8] = (         short *)0;
    sqlstm.sqinds[8] = (         int  )0;
    sqlstm.sqharm[8] = (unsigned long )0;
    sqlstm.sqadto[8] = (unsigned short )0;
    sqlstm.sqtdso[8] = (unsigned short )0;
    sqlstm.sqhstv[9] = (unsigned char  *)&proc2_ot_rst;
    sqlstm.sqhstl[9] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[9] = (         int  )0;
    sqlstm.sqindv[9] = (         short *)0;
    sqlstm.sqinds[9] = (         int  )0;
    sqlstm.sqharm[9] = (unsigned long )0;
    sqlstm.sqadto[9] = (unsigned short )0;
    sqlstm.sqtdso[9] = (unsigned short )0;
    sqlstm.sqhstv[10] = (unsigned char  *)proc2_ot_rstmsg;
    sqlstm.sqhstl[10] = (unsigned long )512;
    sqlstm.sqhsts[10] = (         int  )0;
    sqlstm.sqindv[10] = (         short *)0;
    sqlstm.sqinds[10] = (         int  )0;
    sqlstm.sqharm[10] = (unsigned long )0;
    sqlstm.sqadto[10] = (unsigned short )0;
    sqlstm.sqtdso[10] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}


    
    if (sqlca.sqlcode != 0) {
        log_history(0,0,"[ERR] proc_get_limit_def failed - SQLCODE: %d", sqlca.sqlcode);
        log_history(0,0,"[ERR] SQLERRM: %.70s", sqlca.sqlerrm.sqlerrmc);
        return FALSE;
    }
    
    // 결과 복사
    strcpy(szLimitType, proc2_ot_limit_type);
    *nDayWarnCnt = proc2_ot_day_warn_cnt;
    *nDayLimitCnt = proc2_ot_day_limit_cnt;
    *nMonWarnCnt = proc2_ot_mon_warn_cnt;
    *nMonLimitCnt = proc2_ot_mon_limit_cnt;
    strcpy(szLimitFlag, proc2_ot_limit_flag);
    *nDayAccCnt = proc2_ot_day_acc_cnt;
    *nMonAccCnt = proc2_ot_mon_acc_cnt;
    *nmRST = proc2_ot_rst;
    strcpy(szErrMsg, proc2_ot_rstmsg);
    
    log_history(0,0,"[INF] proc_get_limit_def executed successfully, nmRST[%d]", *nmRST);
    
    return TRUE;
}

// SELECT 쿼리 함수들 (C 스타일)
int select_callback_data(int pid, char callback_list[][512], int* count) {
    log_history(0,0,"[INF] Selecting callback data for PID[%d]", pid);

    select_ptn_id = pid;
    *count = 0;

    /* EXEC SQL DECLARE callback_cursor CURSOR FOR
        SELECT PTN_ID, CALLBACK
        FROM TBL_CALLBACK
        WHERE PTN_ID = :select_ptn_id; */ 


    /* EXEC SQL OPEN callback_cursor; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 20;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = sq0005;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )205;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqcmod = (unsigned int )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&select_ptn_id;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



    if (sqlca.sqlcode != 0) {
        log_history(0,0,"[ERR] Failed to open callback cursor - SQLCODE: %d", sqlca.sqlcode);
        return FALSE;
    }

    while (1) {
        /* EXEC SQL FETCH callback_cursor INTO :select_ptn_id, :select_callback; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 20;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )224;
        sqlstm.selerr = (unsigned short)1;
        sqlstm.sqlpfmem = (unsigned int  )0;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlstm.sqfoff = (         int )0;
        sqlstm.sqfmod = (unsigned int )2;
        sqlstm.sqhstv[0] = (unsigned char  *)&select_ptn_id;
        sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
        sqlstm.sqhsts[0] = (         int  )0;
        sqlstm.sqindv[0] = (         short *)0;
        sqlstm.sqinds[0] = (         int  )0;
        sqlstm.sqharm[0] = (unsigned long )0;
        sqlstm.sqadto[0] = (unsigned short )0;
        sqlstm.sqtdso[0] = (unsigned short )0;
        sqlstm.sqhstv[1] = (unsigned char  *)select_callback;
        sqlstm.sqhstl[1] = (unsigned long )512;
        sqlstm.sqhsts[1] = (         int  )0;
        sqlstm.sqindv[1] = (         short *)0;
        sqlstm.sqinds[1] = (         int  )0;
        sqlstm.sqharm[1] = (unsigned long )0;
        sqlstm.sqadto[1] = (unsigned short )0;
        sqlstm.sqtdso[1] = (unsigned short )0;
        sqlstm.sqphsv = sqlstm.sqhstv;
        sqlstm.sqphsl = sqlstm.sqhstl;
        sqlstm.sqphss = sqlstm.sqhsts;
        sqlstm.sqpind = sqlstm.sqindv;
        sqlstm.sqpins = sqlstm.sqinds;
        sqlstm.sqparm = sqlstm.sqharm;
        sqlstm.sqparc = sqlstm.sqharc;
        sqlstm.sqpadto = sqlstm.sqadto;
        sqlstm.sqptdso = sqlstm.sqtdso;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



        if (sqlca.sqlcode == 1403) { // No data found
            break;
        }

        if (sqlca.sqlcode != 0) {
            log_history(0,0,"[ERR] Failed to fetch callback data - SQLCODE: %d", sqlca.sqlcode);
            /* EXEC SQL CLOSE callback_cursor; */ 

{
            struct sqlexd sqlstm;
            sqlstm.sqlvsn = 13;
            sqlstm.arrsiz = 20;
            sqlstm.sqladtp = &sqladt;
            sqlstm.sqltdsp = &sqltds;
            sqlstm.iters = (unsigned int  )1;
            sqlstm.offset = (unsigned int  )247;
            sqlstm.cud = sqlcud0;
            sqlstm.sqlest = (unsigned char  *)&sqlca;
            sqlstm.sqlety = (unsigned short)4352;
            sqlstm.occurs = (unsigned int  )0;
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}


            return FALSE;
        }

        // null-terminate strings
        select_callback[sizeof(select_callback)-1] = '\0';

        // trim whitespace
        char* trimmed = trim(select_callback, strlen(select_callback));
        if (trimmed && strlen(trimmed) > 0 && *count < 100) {
            strcpy(callback_list[*count], trimmed);
            (*count)++;
            log_history(0,0,"[INF] Added callback: %s", trimmed);
        }
    }

    /* EXEC SQL CLOSE callback_cursor; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 20;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )262;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



    log_history(0,0,"[INF] Loaded %d callback entries", *count);
    return TRUE;
}

int select_dialcode_data(const char* dial_code_type, char dialcode_list[][64], int* count) {
    log_history(0,0,"[INF] Selecting dial code data for type[%s]", dial_code_type);

    strcpy(select_dial_code_type, dial_code_type);
    *count = 0;

    /* EXEC SQL DECLARE dialcode_cursor CURSOR FOR
        SELECT DIAL_CODE_TYPE, DIAL_CODE
        FROM TBL_ALLOW_DIAL_CODE
        WHERE DIAL_CODE_TYPE = :select_dial_code_type; */ 


    /* EXEC SQL OPEN dialcode_cursor; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 20;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = sq0006;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )277;
    sqlstm.selerr = (unsigned short)1;
    sqlstm.sqlpfmem = (unsigned int  )0;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqcmod = (unsigned int )0;
    sqlstm.sqhstv[0] = (unsigned char  *)select_dial_code_type;
    sqlstm.sqhstl[0] = (unsigned long )64;
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



    if (sqlca.sqlcode != 0) {
        log_history(0,0,"[ERR] Failed to open dialcode cursor - SQLCODE: %d", sqlca.sqlcode);
        return FALSE;
    }

    while (1) {
        /* EXEC SQL FETCH dialcode_cursor INTO :select_dial_code_type, :select_dial_code; */ 

{
        struct sqlexd sqlstm;
        sqlstm.sqlvsn = 13;
        sqlstm.arrsiz = 20;
        sqlstm.sqladtp = &sqladt;
        sqlstm.sqltdsp = &sqltds;
        sqlstm.iters = (unsigned int  )1;
        sqlstm.offset = (unsigned int  )296;
        sqlstm.selerr = (unsigned short)1;
        sqlstm.sqlpfmem = (unsigned int  )0;
        sqlstm.cud = sqlcud0;
        sqlstm.sqlest = (unsigned char  *)&sqlca;
        sqlstm.sqlety = (unsigned short)4352;
        sqlstm.occurs = (unsigned int  )0;
        sqlstm.sqfoff = (         int )0;
        sqlstm.sqfmod = (unsigned int )2;
        sqlstm.sqhstv[0] = (unsigned char  *)select_dial_code_type;
        sqlstm.sqhstl[0] = (unsigned long )64;
        sqlstm.sqhsts[0] = (         int  )0;
        sqlstm.sqindv[0] = (         short *)0;
        sqlstm.sqinds[0] = (         int  )0;
        sqlstm.sqharm[0] = (unsigned long )0;
        sqlstm.sqadto[0] = (unsigned short )0;
        sqlstm.sqtdso[0] = (unsigned short )0;
        sqlstm.sqhstv[1] = (unsigned char  *)select_dial_code;
        sqlstm.sqhstl[1] = (unsigned long )64;
        sqlstm.sqhsts[1] = (         int  )0;
        sqlstm.sqindv[1] = (         short *)0;
        sqlstm.sqinds[1] = (         int  )0;
        sqlstm.sqharm[1] = (unsigned long )0;
        sqlstm.sqadto[1] = (unsigned short )0;
        sqlstm.sqtdso[1] = (unsigned short )0;
        sqlstm.sqphsv = sqlstm.sqhstv;
        sqlstm.sqphsl = sqlstm.sqhstl;
        sqlstm.sqphss = sqlstm.sqhsts;
        sqlstm.sqpind = sqlstm.sqindv;
        sqlstm.sqpins = sqlstm.sqinds;
        sqlstm.sqparm = sqlstm.sqharm;
        sqlstm.sqparc = sqlstm.sqharc;
        sqlstm.sqpadto = sqlstm.sqadto;
        sqlstm.sqptdso = sqlstm.sqtdso;
        sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



        if (sqlca.sqlcode == 1403) { // No data found
            break;
        }

        if (sqlca.sqlcode != 0) {
            log_history(0,0,"[ERR] Failed to fetch dialcode data - SQLCODE: %d", sqlca.sqlcode);
            /* EXEC SQL CLOSE dialcode_cursor; */ 

{
            struct sqlexd sqlstm;
            sqlstm.sqlvsn = 13;
            sqlstm.arrsiz = 20;
            sqlstm.sqladtp = &sqladt;
            sqlstm.sqltdsp = &sqltds;
            sqlstm.iters = (unsigned int  )1;
            sqlstm.offset = (unsigned int  )319;
            sqlstm.cud = sqlcud0;
            sqlstm.sqlest = (unsigned char  *)&sqlca;
            sqlstm.sqlety = (unsigned short)4352;
            sqlstm.occurs = (unsigned int  )0;
            sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}


            return FALSE;
        }

        // null-terminate strings
        select_dial_code[sizeof(select_dial_code)-1] = '\0';

        // trim whitespace
        char* trimmed = trim(select_dial_code, strlen(select_dial_code));
        if (trimmed && strlen(trimmed) > 0 && *count < 100) {
            strcpy(dialcode_list[*count], trimmed);
            (*count)++;
            log_history(0,0,"[INF] Added dial code: %s", trimmed);
        }
    }

    /* EXEC SQL CLOSE dialcode_cursor; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 20;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )334;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



    log_history(0,0,"[INF] Loaded %d dial code entries", *count);
    return TRUE;
}

// Pro*C 버전 checkLogon 함수
int checkLogon(char* buff, int type) {
    if (!buff) {
        log_history(0,0,"[ERR] checkLogon: Invalid buffer pointer");
        return -1;
    }

    CPacketUtil packetUtil;
    CLogonDbInfo logonDbInfo;

    char szSIP[100+1];
    char szAPPName[16];
    char szErrMsg[512];
    int nmPID;
    int nmJOB;
    int nmPRT;
    int nmCNT;
    int nmRST;
    int nUrlJob = 0;
    int nRptWait = 0;
    int nRptNoDataSleep = 3;
    char szSenderName[64];
    char szReportName[64];
    char szSenderDBName[64];
    char szReportDBName[64];
    char szLogFilePath[128];
    char szReserve[128];
    char szServerInfo[512];

    // 발송제한 관련 변수들
    char szLimitType[256];
    char szLimitFlag[256];
    int nDayWarnCnt = 0;
    int nDayLimitCnt = 0;
    int nMonWarnCnt = 0;
    int nMonLimitCnt = 0;
    int nDayAccCnt = 0;
    int nMonAccCnt = 0;

    char szCID[64];
    char szPWD[64];
    char szClassify[2];

    // 패킷에서 데이터 추출
    if (type == 1) {
        packetUtil.findValue(buff, "ID", szCID);
        packetUtil.findValue(buff, "PASSWORD", szPWD);
        packetUtil.findValue(buff, "REPORT", szClassify);
    }

    // 첫 번째 프로시저 호출
    alarm(60);
    if (!call_proc_check_mms_login_ext2(szCID, szPWD, szAPPName, szSIP, &nmPID, &nmJOB,
                                        &nUrlJob, &nmPRT, &nmCNT, &nRptWait,
                                        szServerInfo, &nRptNoDataSleep,
                                        szSenderName, szReportName,
                                        szSenderDBName, szReportDBName,
                                        szLogFilePath, szReserve,
                                        &nmRST, szErrMsg)) {
        log_history(0,0,"[ERR] call_proc_check_mms_login_ext2 failed");
        activeProcess = 0;
        alarm(0);
        return -1;
    }
    alarm(0);

    if (nmRST != 0) {
        log_history(0,0,"[ERR] proc_check_mms_login_ext2 failed - nmRST[%d][%s] ErrMsg[%s]", nmRST, szCID, szErrMsg);
        return nmRST;
    }

    // 두 번째 프로시저 호출
    alarm(60);
    if (!call_proc_get_limit_def(nmPID, szLimitType, &nDayWarnCnt, &nDayLimitCnt,
                                 &nMonWarnCnt, &nMonLimitCnt, szLimitFlag,
                                 &nDayAccCnt, &nMonAccCnt, &nmRST, szErrMsg)) {
        log_history(0,0,"[ERR] call_proc_get_limit_def failed");
        alarm(0);
        return -1;
    }
    alarm(0);

    if (nmRST != 0) {
        log_history(0,0,"[ERR] proc_get_limit_def failed - nmRST[%d] ErrMsg[%s]", nmRST, szErrMsg);
        return nmRST;
    }

    // 결과 데이터 설정
    memset(&logonDbInfo, 0x00, sizeof(logonDbInfo));

    // 안전한 문자열 복사 및 trim
    char* trimmed_ptr;

    trimmed_ptr = trim(szAPPName, strlen(szAPPName));
    if (trimmed_ptr) {
        strncpy(logonDbInfo.szAPPName, trimmed_ptr, sizeof(logonDbInfo.szAPPName)-1);
    }

    trimmed_ptr = trim(szSIP, strlen(szSIP));
    if (trimmed_ptr) {
        strncpy(logonDbInfo.szSIP, trimmed_ptr, sizeof(logonDbInfo.szSIP)-1);
    }

    logonDbInfo.nmPID = nmPID;
    logonDbInfo.nmJOB = nmJOB;
    logonDbInfo.nUrlJob = nUrlJob;
    logonDbInfo.nmPRT = nmPRT;
    logonDbInfo.nmCNT = nmCNT;
    logonDbInfo.nRptWait = nRptWait;

    trimmed_ptr = trim(szServerInfo, strlen(szServerInfo));
    if (trimmed_ptr) {
        strncpy(logonDbInfo.szServerInfo, trimmed_ptr, sizeof(logonDbInfo.szServerInfo)-1);
    }

    logonDbInfo.nRptNoDataSleep = nRptNoDataSleep;

    trimmed_ptr = trim(szSenderName, strlen(szSenderName));
    if (trimmed_ptr) {
        strncpy(logonDbInfo.szSenderName, trimmed_ptr, sizeof(logonDbInfo.szSenderName)-1);
    }

    trimmed_ptr = trim(szReportName, strlen(szReportName));
    if (trimmed_ptr) {
        strncpy(logonDbInfo.szReportName, trimmed_ptr, sizeof(logonDbInfo.szReportName)-1);
    }

    trimmed_ptr = trim(szSenderDBName, strlen(szSenderDBName));
    if (trimmed_ptr) {
        strncpy(logonDbInfo.szSenderDBName, trimmed_ptr, sizeof(logonDbInfo.szSenderDBName)-1);
    }

    trimmed_ptr = trim(szReportDBName, strlen(szReportDBName));
    if (trimmed_ptr) {
        strncpy(logonDbInfo.szReportDBName, trimmed_ptr, sizeof(logonDbInfo.szReportDBName)-1);
    }

    trimmed_ptr = trim(szLogFilePath, strlen(szLogFilePath));
    if (trimmed_ptr) {
        strncpy(logonDbInfo.szLogFilePath, trimmed_ptr, sizeof(logonDbInfo.szLogFilePath)-1);
    }

    trimmed_ptr = trim(szReserve, strlen(szReserve));
    if (trimmed_ptr) {
        strncpy(logonDbInfo.szReserve, trimmed_ptr, sizeof(logonDbInfo.szReserve)-1);
    }

    // 발송제한 정보 설정
    trimmed_ptr = trim(szLimitType, strlen(szLimitType));
    if (trimmed_ptr) {
        logonDbInfo.szLimitType[0] = trimmed_ptr[0];
    }

    trimmed_ptr = trim(szLimitFlag, strlen(szLimitFlag));
    if (trimmed_ptr) {
        logonDbInfo.szLimitFlag[0] = trimmed_ptr[0];
    }

    logonDbInfo.nDayWarnCnt = nDayWarnCnt;
    logonDbInfo.nDayLimitCnt = nDayLimitCnt;
    logonDbInfo.nMonWarnCnt = nMonWarnCnt;
    logonDbInfo.nMonLimitCnt = nMonLimitCnt;
    logonDbInfo.nDayAccCnt = nDayAccCnt;
    logonDbInfo.nMonAccCnt = nMonAccCnt;

    strcpy(logonDbInfo.szCID, szCID);
    strcpy(logonDbInfo.szPWD, szPWD);
    logonDbInfo.classify = szClassify[0];

    memset(buff, 0x00, SOCKET_BUFF);
    memcpy(buff, &logonDbInfo, sizeof(logonDbInfo));

    log_history(0,0,"[INF] checkLogon completed successfully");
    return 0;
}

// 간단한 프로토콜 처리 함수들
int classifyProtocol(CKSSocket& newSockfd, int size, char* bindPacket) {
    if (strstr(bindPacket, "BEGIN CONNECT") != NULL) {
        log_history(0,0,"[INF] socket_domain read - [%s]", bindPacket);
        logonTypeMMS(newSockfd, 1, bindPacket);
        return 0;
    } else if (strstr(bindPacket, "BEGIN CALLBACK") != NULL) {
        log_history(0,0,"[INF] socket_domain read - [%s]", bindPacket);
        getCallback(newSockfd, 1, bindPacket);
        return 0;
    } else if (strstr(bindPacket, "BEGIN DIALCODE") != NULL) {
        log_history(0,0,"[INF] socket_domain read - [%s]", bindPacket);
        getDialCode(newSockfd, 1, bindPacket);
        return 0;
    } else {
        log_history(0,0,"[INF] socket_domain read - [%s]", bindPacket);
        logonType(newSockfd, 1, bindPacket);
        return 0;
    }
}

int logonTypeMMS(CKSSocket& newSockfd, int type, char* bindPacket) {
    int ret = -1;
    TypeMsgBindAck ack;
    memset(&ack, 0x00, sizeof(ack));
    strcpy(ack.header.msgType, "2");

    ret = checkLogon(bindPacket, type);
    if (ret == 0) {
        strcpy(ack.header.result, "0000");
        ack.header.leng = sizeof(TypeMsgBindAck) - sizeof(Header);
        memcpy(ack.data, bindPacket, sizeof(CLogonDbInfo));
    } else {
        strcpy(ack.header.result, "9999");
        ack.header.leng = sizeof(TypeMsgBindAck) - sizeof(Header);
        memset(ack.data, 0x00, sizeof(ack.data));
    }

    ret = newSockfd.send((char*)&ack, sizeof(ack));
    if (ret < 0) {
        log_history(0,0,"[ERR] socket_domain send error[%s]", strerror(errno));
    }
    return 0;
}

int logonType(CKSSocket& newSockfd, int type, char* bindPacket) {
    int ret = -1;
    TypeMsgBindAck ack;
    memset(&ack, 0x00, sizeof(ack));
    strcpy(ack.header.msgType, "2");

    ret = checkLogon(bindPacket, type);
    if (ret == 0) {
        strcpy(ack.header.result, "0000");
        ack.header.leng = sizeof(TypeMsgBindAck) - sizeof(Header);
        memcpy(ack.data, bindPacket, sizeof(CLogonDbInfo));
    } else {
        strcpy(ack.header.result, "9999");
        ack.header.leng = sizeof(TypeMsgBindAck) - sizeof(Header);
        memset(ack.data, 0x00, sizeof(ack.data));
    }

    ret = newSockfd.send((char*)&ack, sizeof(ack));
    if (ret < 0) {
        log_history(0,0,"[ERR] socket_domain send error[%s]", strerror(errno));
    }
    return 0;
}

int getCallback(CKSSocket& newSockfd, int type, char* bindPacket) {
    int ret = -1;
    TypeMsgBindAck ack;
    memset(&ack, 0x00, sizeof(ack));
    strcpy(ack.header.msgType, "2");

    char callback_list[100][512];
    int count = 0;
    ret = loadCallback(bindPacket, type, callback_list, &count);

    if (ret == 0) {
        strcpy(ack.header.result, "0000");
        ack.header.leng = sizeof(TypeMsgBindAck) - sizeof(Header);
        // 콜백 데이터를 ack.data에 설정하는 로직 필요
    } else {
        strcpy(ack.header.result, "9999");
        ack.header.leng = sizeof(TypeMsgBindAck) - sizeof(Header);
        memset(ack.data, 0x00, sizeof(ack.data));
    }

    ret = newSockfd.send((char*)&ack, sizeof(ack));
    if (ret < 0) {
        log_history(0,0,"[ERR] socket_domain send error[%s]", strerror(errno));
    }
    return 0;
}

int loadCallback(char* buff, int type, char callback_list[][512], int* count) {
    TypeMsgGetCallback* pGetCallback = (TypeMsgGetCallback*)buff;

    if (!select_callback_data(pGetCallback->pid, callback_list, count)) {
        log_history(0,0,"[ERR] Failed to load callback data");
        activeProcess = 0;
        return -1;
    }

    return 0;
}

int getDialCode(CKSSocket& newSockfd, int type, char* bindPacket) {
    int ret = -1;
    TypeMsgBindAck ack;
    memset(&ack, 0x00, sizeof(ack));
    strcpy(ack.header.msgType, "2");

    char dialcode_list[100][64];
    int count = 0;
    ret = loadDialCode(bindPacket, type, dialcode_list, &count);

    if (ret == 0) {
        strcpy(ack.header.result, "0000");
        ack.header.leng = sizeof(TypeMsgBindAck) - sizeof(Header);
        // 다이얼 코드 데이터를 ack.data에 설정하는 로직 필요
    } else {
        strcpy(ack.header.result, "9999");
        ack.header.leng = sizeof(TypeMsgBindAck) - sizeof(Header);
        memset(ack.data, 0x00, sizeof(ack.data));
    }

    ret = newSockfd.send((char*)&ack, sizeof(ack));
    if (ret < 0) {
        log_history(0,0,"[ERR] socket_domain send error[%s]", strerror(errno));
    }
    return 0;
}

int loadDialCode(char* buff, int type, char dialcode_list[][64], int* count) {
    TypeMsgGetDialCode* pGetDialCode = (TypeMsgGetDialCode*)buff;

    if (!select_dialcode_data(pGetDialCode->dial_code_type, dialcode_list, count)) {
        log_history(0,0,"[ERR] Failed to load dial code data");
        activeProcess = 0;
        return -1;
    }

    return 0;
}

int configParse(char* confFile) {
    CKSConfig conf;

    if (conf.loadConf(confFile) < 0) {
        return -1;
    }

    conf.strncpy2(gConf.logonDBName, conf.FetchEntry("domain.logondb"), 64);
    if (gConf.logonDBName == NULL) {
        strcpy(gConf.logonDBName, "");
    }
    conf.strncpy2(gConf.dbID, conf.FetchEntry("db.id"), 16);
    if (gConf.dbID == NULL) {
        strcpy(gConf.dbID, "");
    }
    conf.strncpy2(gConf.dbPASS, conf.FetchEntry("db.pass"), 16);
    if (gConf.dbPASS == NULL) {
        strcpy(gConf.dbPASS, "");
    }
    conf.strncpy2(gConf.dbSID, conf.FetchEntry("db.sid"), 16);
    if (gConf.dbSID == NULL) {
        strcpy(gConf.dbSID, "");
    }

    return 0;
}

// main 함수
int main(int argc, char* argv[]) {
    char logMsg[256];
    char buff[SOCKET_BUFF];
    CKSSocket svrSockfd;
    CKSSocket newSockfd;

    if (ml_sub_init(PROCESS_NO, PROCESS_NAME, (char*)0, (char*)0) < 0) {
        perror("ml_sub_init Error.");
        ml_sub_end();
        return -1;
    }

    if (configParse(argv[1]) < 0) {
        log_history(0,0,"[ERR] config file parse error");
        goto END;
    }

    Init_Server();

    // 시그널 핸들러 등록 (Init_Server() 이후에 등록하여 CloseProcess를 덮어쓰기)
    signal(SIGALRM, alarm_handler);
    signal(SIGSEGV, segv_handler);
    signal(SIGABRT, segv_handler);
    signal(SIGHUP, term_handler);
    signal(SIGTERM, term_handler);
    signal(SIGINT, term_handler);
    signal(SIGQUIT, term_handler);
    signal(SIGUSR1, term_handler);

    log_history(0,0,"[INFO] Pro*C signal handlers registered after Init_Server(), overriding all CloseProcess signals");

    ret = svrSockfd.createDomainNon(gConf.logonDBName);
    if (ret < 0) {
        log_history(0,0,"[ERR] socket_domain logonDB create failed %s", strerror(errno));
        goto END;
    }

    if (!proc_connect()) {
        log_history(0,0,"[ERR] Pro*C db connect failed\n- SID[%s]\n- ID[%s]\n- PW[%s]", gConf.dbSID, gConf.dbID, gConf.dbPASS);
        goto END;
    }

    log_history(0,0,"[DEBUG] About to enter main loop, activeProcess=%d", activeProcess);

    while (activeProcess) {
        log_history(0,0,"[DEBUG] Loop iteration start, activeProcess=%d", activeProcess);

        hNewSocket = -1;

        wait_a_moment(10000);
        log_history(0,0,"[DEBUG] After wait_a_moment");

        ret = svrSockfd.select(0, 50000);
        log_history(0,0,"[DEBUG] select returned [%d]", ret);

        if (ret < 0) {
            if (errno == EINTR) {
                log_history(0,0,"[WARN] select interrupted by signal (EINTR), continuing...");
                continue;
            } else {
                log_history(0,0,"[ERR] select failed with errno %d: %s", errno, strerror(errno));
                log_history(0,0,"[ERR] Critical select error, terminating process");
                activeProcess = 0;
                break;
            }
        } else if (ret > 0) {
            hNewSocket = svrSockfd.accept();
            log_history(0,0,"[INF] accept returned socket [%d]", hNewSocket);
        }

        if (hNewSocket <= 0) {
            continue;
        }

        // new connection
        newSockfd.attach(hNewSocket);
        memset(buff, 0x00, sizeof(buff));
        log_history(0,0,"[INF] New connection established, waiting for data");

        ret = newSockfd.rcvmsg(buff);
        log_history(0,0,"[INF] rcvmsg returned [%d]", ret);
        if (ret == 0) {
            log_history(0,0,"[INF] Client disconnected");
            newSockfd.close();
            continue;
        }

        if (ret < 0) {
            log_history(0,0,"[ERR] rcvmsg error[%s]", strerror(errno));
            newSockfd.close();
            continue;
        }

        log_history(0,0,"[INF] Received data, classifying protocol");
        classifyProtocol(newSockfd, ret, buff);

        newSockfd.send("1", 2);
        newSockfd.close();
    }

END:
    svrSockfd.close();
    proc_disconnect();
    sprintf(logMsg, "close [%s]", PROCESS_NAME);
    monitoring(logMsg, 0, 0);
    ml_sub_end();

    return 0;
}
