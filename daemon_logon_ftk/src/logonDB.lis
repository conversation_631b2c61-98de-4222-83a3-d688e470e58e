
Pro*C/C++: Release 21.0.0.0.0 - Production on Thu Jul 31 01:18:00 2025
Version 21.18.0.0.0

Copyright (c) 1982, 2025, Oracle and/or its affiliates.  All rights reserved.

Error at line 0, column 0 in file logonDB.pc
PCC-S-02201, Encountered the symbol "<eof>" when expecting one of the following
:

   ; : an identifier, end-exec, random_terminal

Error at line 0, column 0 in file logonDB.pc
PCC-F-02102, Fatal error while doing C preprocessing
Error at line 4, column 10 in file ../inc/logonDB.h
     4  #include <iostream>
     4  .........1
     4  PCC-S-02015, unable to open include file
Error at line 5, column 10 in file ../inc/logonDB.h
     5  #include <set>
     5  .........1
     5  PCC-S-02015, unable to open include file
Error at line 26, column 49 in file /usr/include/linux/stddef.h
    26  #define __struct_group(TAG, NAME, ATTRS, MEMBERS...) \
    26  ................................................1
    26  PCC-S-02014, Encountered the symbol "..." when expecting one of the fol
        lowing:
        
           , )
        The symbol "," was substituted for "..." to continue.
        
Error at line 15, column 10 in file /home/<USER>/library/kssocket.h
    15  #include <string>
    15  .........1
    15  PCC-S-02015, unable to open include file
Error at line 18, column 7 in file /home/<USER>/library/kssocket.h
    18  class CKSSocket
    18  ......1
    18  PCC-S-02201, Encountered the symbol "CKSSocket" when expecting one of t
        he following:
        
           ; , = ( [
        
Error at line 49, column 13 in file /home/<USER>/library/kssocket.h
    49          std::string packet;
    49  ............1
    49  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier, was inserted before ":" to continue.
        
Error at line 40, column 26 in file ../inc/logonDB.h
    40  bool orapp_connect(ORAPP::Connection &db, const char *tns, const char *
        user, const char *pass); 
    40  .........................1
    40  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier," was substituted for ":" to continue.
        
Error at line 41, column 29 in file ../inc/logonDB.h
    41  bool orapp_disconnect(ORAPP::Connection &db);
    41  ............................1
    41  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier," was substituted for ":" to continue.
        
Error at line 42, column 22 in file ../inc/logonDB.h
    42  int checkLogon(ORAPP::Connection &db,char* buff,int type);
    42  .....................1
    42  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier," was substituted for ":" to continue.
        

Pro*C/C++: Release 21.0.0.0.0 - Production on Thu Jul 31 01:18:00 2025
Version 21.18.0.0.0

Copyright (c) 1982, 2025, Oracle and/or its affiliates.  All rights reserved.

Error at line 43, column 25 in file ../inc/logonDB.h
    43  int checkLogonMMS(ORAPP::Connection &db,char* buff,int type);
    43  ........................1
    43  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier," was substituted for ":" to continue.
        
Error at line 45, column 28 in file ../inc/logonDB.h
    45  int classifyProtocol(ORAPP::Connection &db,CKSSocket& newSockfd,int siz
        e,char* bindPacket);
    45  ...........................1
    45  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier," was substituted for ":" to continue.
        
Error at line 46, column 21 in file ../inc/logonDB.h
    46  int logonType(ORAPP::Connection &db,CKSSocket& newSockfd,int type,char*
         bindPacket);
    46  ....................1
    46  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier," was substituted for ":" to continue.
        
Error at line 47, column 24 in file ../inc/logonDB.h
    47  int logonTypeMMS(ORAPP::Connection &db,CKSSocket& newSockfd,int type,ch
        ar* bindPacket);
    47  .......................1
    47  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier," was substituted for ":" to continue.
        
Error at line 50, column 23 in file ../inc/logonDB.h
    50  int getCallback(ORAPP::Connection &db,CKSSocket& newSockfd,int type,cha
        r* bindPacket);
    50  ......................1
    50  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier," was substituted for ":" to continue.
        
Error at line 51, column 24 in file ../inc/logonDB.h
    51  int loadCallback(ORAPP::Connection &db, char* buff, int type, set<strin
        g> &set_callback_list);
    51  .......................1
    51  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier," was substituted for ":" to continue.
        
Error at line 52, column 23 in file ../inc/logonDB.h
    52  int getDialCode(ORAPP::Connection &db, CKSSocket& newSockfd, int type, 
        char* bindPacket);
    52  ......................1
    52  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier," was substituted for ":" to continue.
        

Pro*C/C++: Release 21.0.0.0.0 - Production on Thu Jul 31 01:18:00 2025
Version 21.18.0.0.0

Copyright (c) 1982, 2025, Oracle and/or its affiliates.  All rights reserved.

Error at line 53, column 24 in file ../inc/logonDB.h
    53  int loadDialCode(ORAPP::Connection &db, char* buff, int type, set<strin
        g> &set_dialcode_list);
    53  .......................1
    53  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier," was substituted for ":" to continue.
        
Error at line 9, column 10 in file logonDB.pc
     9  #include <iostream>
     9  .........1
     9  PCC-S-02015, unable to open include file
Error at line 10, column 10 in file logonDB.pc
    10  #include <set>
    10  .........1
    10  PCC-S-02015, unable to open include file
Error at line 14, column 1 in file logonDB.pc
    14  EXEC SQL INCLUDE sqlca;
    14  1
    14  PCC-S-02015, unable to open include file
Error at line 279, column 39 in file logonDB.pc
   279  int select_callback_data(int pid, std::set<std::string> &set_callback_l
        ist) {
   279  ......................................1
   279  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier, was inserted before ":" to continue.
        
Error at line 279, column 48 in file logonDB.pc
   279  int select_callback_data(int pid, std::set<std::string> &set_callback_l
        ist) {
   279  ...............................................1
   279  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier, was inserted before ":" to continue.
        
Error at line 315, column 42 in file logonDB.pc
   315              set_callback_list.insert(std::string(trimmed));
   315  .........................................1
   315  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier, was inserted before ":" to continue.
        
Error at line 326, column 58 in file logonDB.pc
   326  int select_dialcode_data(const char* dial_code_type, std::set<std::stri
        ng> &set_dialcode_list) {
   326  .........................................................1
   326  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier, was inserted before ":" to continue.
        
Error at line 326, column 67 in file logonDB.pc
   326  int select_dialcode_data(const char* dial_code_type, std::set<std::stri
        ng> &set_dialcode_list) {
   326  ..................................................................1
   326  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier, was inserted before ":" to continue.
        

Pro*C/C++: Release 21.0.0.0.0 - Production on Thu Jul 31 01:18:00 2025
Version 21.18.0.0.0

Copyright (c) 1982, 2025, Oracle and/or its affiliates.  All rights reserved.

Error at line 362, column 42 in file logonDB.pc
   362              set_dialcode_list.insert(std::string(trimmed));
   362  .........................................1
   362  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier, was inserted before ":" to continue.
        
Error at line 632, column 9 in file logonDB.pc
   632      std::set<std::string> set_callback_list;
   632  ........1
   632  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier, was inserted before ":" to continue.
        
Error at line 632, column 18 in file logonDB.pc
   632      std::set<std::string> set_callback_list;
   632  .................1
   632  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier, was inserted before ":" to continue.
        
Error at line 653, column 44 in file logonDB.pc
   653  int loadCallback(char* buff, int type, std::set<std::string> &set_callb
        ack_list) {
   653  ...........................................1
   653  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier, was inserted before ":" to continue.
        
Error at line 653, column 53 in file logonDB.pc
   653  int loadCallback(char* buff, int type, std::set<std::string> &set_callb
        ack_list) {
   653  ....................................................1
   653  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier, was inserted before ":" to continue.
        
Error at line 672, column 9 in file logonDB.pc
   672      std::set<std::string> set_dialcode_list;
   672  ........1
   672  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier, was inserted before ":" to continue.
        
Error at line 672, column 18 in file logonDB.pc
   672      std::set<std::string> set_dialcode_list;
   672  .................1
   672  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier, was inserted before ":" to continue.
        

Pro*C/C++: Release 21.0.0.0.0 - Production on Thu Jul 31 01:18:00 2025
Version 21.18.0.0.0

Copyright (c) 1982, 2025, Oracle and/or its affiliates.  All rights reserved.

Error at line 693, column 44 in file logonDB.pc
   693  int loadDialCode(char* buff, int type, std::set<std::string> &set_dialc
        ode_list) {
   693  ...........................................1
   693  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier, was inserted before ":" to continue.
        
Error at line 693, column 53 in file logonDB.pc
   693  int loadDialCode(char* buff, int type, std::set<std::string> &set_dialc
        ode_list) {
   693  ....................................................1
   693  PCC-S-02201, Encountered the symbol ":" when expecting one of the follo
        wing:
        
           an identifier,
        The symbol "an identifier, was inserted before ":" to continue.
        
