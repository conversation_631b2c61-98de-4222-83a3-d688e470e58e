#ifndef _LOGON_DB_H_
#define _LOGON_DB_H_

#include <iostream>
#include <set>

#include "stdafx.h"
#include "kssocket.h"
#include "smsPacketStruct.h"
#include "logonDbInfo.h"
#include "ksconfig.h"
#include "packetUtil.h"
#include "DatabaseORA_MMS.h"

// DatabaseORA_MMS 사용
// #include "../../libsrc/odpi/inc/odpi_wrapper.hh"



using namespace std;


int activeProcess = TRUE;
struct _message_info	message_info;
struct _shm_info *shm_info;
char PROCESS_NO[ 7], PROCESS_NAME[36];



class CConfigLogonDB{
    public:
        char logonDBName[64];
        char dbID[16];
        char dbPASS[16];
        char dbSID[16];
        char dbuid[64];  // senderFtalkProDB.cpp와 동일한 필드 추가
        char dbdsn[64];  // senderFtalkProDB.cpp와 동일한 필드 추가
};

CConfigLogonDB gConf;
KSKYB::CDatabaseORA g_oracle;


// DatabaseORA_MMS 사용으로 변경
bool oracle_connect();
bool oracle_disconnect();
int checkLogon(char* buff,int type);
int checkLogonMMS(char* buff,int type);
int configParse(char* file);
int classifyProtocol(CKSSocket& newSockfd,int size,char* bindPacket);
int logonType(CKSSocket& newSockfd,int type,char* bindPacket);
int logonTypeMMS(CKSSocket& newSockfd,int type,char* bindPacket);

/* 변작금지 기능 개발에 의한 추가 함수*/
int getCallback(CKSSocket& newSockfd,int type,char* bindPacket);
int loadCallback(char* buff, int type, set<string> &set_callback_list);
int getDialCode(CKSSocket& newSockfd, int type, char* bindPacket);
int loadDialCode(char* buff, int type, set<string> &set_dialcode_list);

#endif


